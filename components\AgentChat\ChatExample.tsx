'use client';

import React, { useState } from 'react';
import { ChatBox, Message } from './ChatBox';

// Example usage component
export const ChatExample: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Hello! I\'m your AI assistant. How can I help you today?',
      sender: 'agent',
      timestamp: new Date(Date.now() - 60000), // 1 minute ago
    },
  ]);
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Simulate AI response delay
    setTimeout(() => {
      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: generateMockResponse(content),
        sender: 'agent',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, agentMessage]);
      setIsLoading(false);
    }, 1000 + Math.random() * 2000); // 1-3 second delay
  };

  const generateMockResponse = (userMessage: string): string => {
    const responses = [
      `I understand you're asking about "${userMessage}". Let me help you with that.`,
      `That's an interesting question about "${userMessage}". Here's what I think...`,
      `Thanks for your message about "${userMessage}". I'd be happy to assist you.`,
      `I see you mentioned "${userMessage}". Let me provide some information on that topic.`,
      `Great question! Regarding "${userMessage}", here's my response...`,
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <ChatBox
        messages={messages}
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        placeholder="Ask me anything..."
        className="h-[500px]"
      />
    </div>
  );
};

export default ChatExample;
