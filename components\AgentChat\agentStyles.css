/* Custom styles for AgentChat components */

/* Chat bubble animations */
.chat-message-enter {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.chat-message-enter-active {
  opacity: 1;
  transform: translateY(0);
}

/* Typing indicator animation */
.typing-dot {
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Custom scrollbar for chat area */
.chat-scroll-area [data-radix-scroll-area-scrollbar] {
  width: 6px;
}

.chat-scroll-area [data-radix-scroll-area-thumb] {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.chat-scroll-area [data-radix-scroll-area-thumb]:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Message bubble hover effects */
.message-bubble {
  transition: all 0.2s ease;
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Input focus styles */
.chat-input:focus {
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
}

/* Send button pulse animation when message is ready */
.send-button-ready {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 hsl(var(--primary) / 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px hsl(var(--primary) / 0);
  }
  100% {
    box-shadow: 0 0 0 0 hsl(var(--primary) / 0);
  }
}

/* Online status indicator */
.status-indicator {
  position: relative;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: currentColor;
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .message-bubble:hover {
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
  }
}
