'use client';

import React from 'react';
import { Bot, User } from 'lucide-react';

interface MessageProps {
  role: 'user' | 'agent';
  content: string;
  className?: string;
}

export const Message: React.FC<MessageProps> = ({
  role,
  content,
  className = ""
}) => {
  const isUser = role === 'user';
  const isAgent = role === 'agent';

  return (
    <div
      className={`flex gap-3 ${
        isUser ? 'justify-end' : 'justify-start'
      } ${className}`}
    >
      {/* Agent Avatar (left side) */}
      {isAgent && (
        <div className="flex-shrink-0">
          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
            <Bot className="h-4 w-4 text-primary" />
          </div>
        </div>
      )}

      {/* Message Bubble */}
      <div
        className={`max-w-[80%] rounded-lg px-4 py-2 shadow-sm ${
          isUser
            ? 'bg-primary text-primary-foreground rounded-br-sm'
            : 'bg-muted text-foreground rounded-bl-sm'
        }`}
      >
        <p className="text-sm whitespace-pre-wrap break-words leading-relaxed">
          {content}
        </p>

        {/* Optional Timestamp */}
        {showTimestamp && (
          <p className={`text-xs mt-1 ${
            isUser
              ? 'text-primary-foreground/70'
              : 'text-muted-foreground'
          }`}>
            {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </p>
        )}
      </div>

      {/* User Avatar (right side) */}
      {isUser && showAvatar && (
        <div className="flex-shrink-0">
          <div className="h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center">
            <User className="h-4 w-4 text-primary" />
          </div>
        </div>
      )}
    </div>
  );
};

export default Message;
