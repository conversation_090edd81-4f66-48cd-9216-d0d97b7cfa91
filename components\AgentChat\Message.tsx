'use client';

import React from 'react';
import { Bot, User } from 'lucide-react';
import { Message as MessageType } from './ChatBox';

interface MessageProps {
  message: MessageType;
  className?: string;
}

export const Message: React.FC<MessageProps> = ({ message, className = "" }) => {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div
      className={`flex gap-3 ${
        message.sender === 'user' ? 'justify-end' : 'justify-start'
      } ${className}`}
    >
      {message.sender === 'agent' && (
        <div className="flex-shrink-0">
          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
            <Bot className="h-4 w-4 text-primary" />
          </div>
        </div>
      )}
      
      <div
        className={`max-w-[80%] rounded-lg px-3 py-2 ${
          message.sender === 'user'
            ? 'bg-primary text-primary-foreground ml-auto'
            : 'bg-muted text-foreground'
        }`}
      >
        <p className="text-sm whitespace-pre-wrap break-words">
          {message.content}
        </p>
        <p className={`text-xs mt-1 ${
          message.sender === 'user' 
            ? 'text-primary-foreground/70' 
            : 'text-muted-foreground'
        }`}>
          {formatTime(message.timestamp)}
        </p>
      </div>

      {message.sender === 'user' && (
        <div className="flex-shrink-0">
          <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
            <User className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>
      )}
    </div>
  );
};

export default Message;
